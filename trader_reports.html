<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقارير التجار - نظام CCIS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
        }
        
        .header-section {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .report-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 5px solid #3498db;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .report-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #ecf0f1;
        }
        
        .report-icon {
            font-size: 2.5rem;
            margin-left: 15px;
            color: #3498db;
        }
        
        .report-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
            margin: 0;
        }
        
        .sql-code {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 15px 0;
            border: 1px solid #34495e;
        }
        
        .nav-pills .nav-link {
            border-radius: 25px;
            margin: 0 5px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .nav-pills .nav-link.active {
            background: linear-gradient(45deg, #3498db, #2980b9);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .suggestion-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 15px 0;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .feature-list li:before {
            content: "✓";
            color: #2ecc71;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .btn-custom {
            background: linear-gradient(45deg, #3498db, #2980b9);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.4);
            color: white;
        }
        
        .trader-selector {
            background: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .alert-custom {
            border-radius: 15px;
            border: none;
            padding: 20px;
            margin: 15px 0;
        }
        
        .table-responsive {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .table th {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .table td {
            border-color: #ecf0f1;
            vertical-align: middle;
        }
        
        .badge-custom {
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
        }
        
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                padding: 15px;
            }
            
            .report-card {
                padding: 15px;
            }
            
            .sql-code {
                font-size: 0.8rem;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <h1><i class="fas fa-chart-line"></i> نظام تقارير التجار الشامل</h1>
            <p class="mb-0">تقارير احترافية ومتقدمة لإدارة حسابات التجار في نظام CCIS</p>
        </div>

        <!-- Trader Selection -->
        <div class="trader-selector">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <label for="traderSelect" class="form-label fw-bold">اختر التاجر:</label>
                    <select class="form-select" id="traderSelect">
                        <option value="">-- اختر التاجر --</option>
                        <option value="1">عبدالرضا طاهر حسين (count)</option>
                        <option value="2">علي (acc123)</option>
                        <option value="3">reda (acc124)</option>
                        <option value="4">scsd (ccx11231)</option>
                    </select>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-custom" onclick="generateReports()">
                        <i class="fas fa-file-alt"></i> إنشاء التقارير
                    </button>
                    <button class="btn btn-outline-secondary ms-2" onclick="exportToPDF()">
                        <i class="fas fa-file-pdf"></i> تصدير PDF
                    </button>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <ul class="nav nav-pills justify-content-center mb-4" id="reportTabs">
            <li class="nav-item">
                <a class="nav-link active" data-bs-toggle="pill" href="#financial-reports">
                    <i class="fas fa-money-bill-wave"></i> التقارير المالية
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="pill" href="#container-reports">
                    <i class="fas fa-shipping-fast"></i> تقارير الحاويات
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="pill" href="#fees-reports">
                    <i class="fas fa-receipt"></i> الغرامات والرسوم
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="pill" href="#analytics-reports">
                    <i class="fas fa-chart-bar"></i> التحليلات المتقدمة
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="pill" href="#development-suggestions">
                    <i class="fas fa-lightbulb"></i> اقتراحات التطوير
                </a>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content">
            <!-- Financial Reports Tab -->
            <div class="tab-pane fade show active" id="financial-reports">
                <!-- Statement of Account Report -->
                <div class="report-card">
                    <div class="report-header">
                        <i class="fas fa-file-invoice-dollar report-icon"></i>
                        <h3 class="report-title">كشف حساب التاجر</h3>
                    </div>
                    <p class="text-muted">عرض جميع المعاملات المالية للتاجر مع الأرصدة الجارية والتفاصيل الكاملة</p>
                    
                    <div class="alert alert-info alert-custom">
                        <i class="fas fa-info-circle"></i>
                        <strong>الوصف:</strong> يعرض هذا التقرير جميع المعاملات المالية مرتبة زمنياً مع حساب الرصيد الجاري
                    </div>
                    
                    <div class="sql-code">
SELECT 
    fd.document_date AS 'تاريخ المعاملة',
    CASE fd.document_type
        WHEN 'receipt' THEN 'إيصال استلام'
        WHEN 'payment' THEN 'إيصال دفع'
        WHEN 'expense' THEN 'مصروف'
        WHEN 'transfer' THEN 'تحويل'
        WHEN 'capital' THEN 'رأس مال'
    END AS 'نوع المعاملة',
    fd.amount AS 'المبلغ',
    fd.notes AS 'الملاحظات',
    CASE 
        WHEN fd.document_type IN ('receipt', 'capital') THEN fd.amount 
        ELSE 0 
    END AS 'مدين',
    CASE 
        WHEN fd.document_type IN ('payment', 'expense') THEN fd.amount 
        ELSE 0 
    END AS 'دائن',
    @running_balance := @running_balance + 
        CASE WHEN fd.document_type IN ('receipt', 'capital') THEN fd.amount 
             ELSE -fd.amount END AS 'الرصيد'
FROM financial_documents fd
CROSS JOIN (SELECT @running_balance := 0) r
WHERE fd.trader_id = ? AND fd.deleted_at IS NULL
ORDER BY fd.document_date, fd.id;
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <button class="btn btn-custom" onclick="executeQuery('statement')">
                                <i class="fas fa-play"></i> تشغيل الاستعلام
                            </button>
                        </div>
                        <div class="col-md-6 text-end">
                            <small class="text-muted">آخر تحديث: <span id="lastUpdate">--</span></small>
                        </div>
                    </div>
                </div>

                <!-- Monthly Income/Expense Report -->
                <div class="report-card">
                    <div class="report-header">
                        <i class="fas fa-calendar-alt report-icon"></i>
                        <h3 class="report-title">تقرير الإيرادات والمصروفات الشهري</h3>
                    </div>
                    <p class="text-muted">تحليل مفصل للإيرادات والمصروفات مقسمة حسب الأشهر مع حساب صافي الربح</p>
                    
                    <div class="sql-code">
SELECT 
    YEAR(document_date) as 'السنة',
    MONTH(document_date) as 'الشهر',
    MONTHNAME(document_date) as 'اسم الشهر',
    SUM(CASE WHEN document_type = 'receipt' THEN amount ELSE 0 END) as 'إجمالي الإيصالات',
    SUM(CASE WHEN document_type = 'payment' THEN amount ELSE 0 END) as 'إجمالي المدفوعات',
    SUM(CASE WHEN document_type = 'expense' THEN amount ELSE 0 END) as 'إجمالي المصروفات',
    (SUM(CASE WHEN document_type = 'receipt' THEN amount ELSE 0 END) - 
     SUM(CASE WHEN document_type IN ('payment', 'expense') THEN amount ELSE 0 END)) as 'صافي الرصيد'
FROM financial_documents 
WHERE trader_id = ? AND deleted_at IS NULL
GROUP BY YEAR(document_date), MONTH(document_date)
ORDER BY YEAR(document_date) DESC, MONTH(document_date) DESC;
                    </div>
                    
                    <div class="chart-container">
                        <canvas id="monthlyChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Container Reports Tab -->
            <div class="tab-pane fade" id="container-reports">
                <!-- Container Status Report -->
                <div class="report-card">
                    <div class="report-header">
                        <i class="fas fa-boxes report-icon"></i>
                        <h3 class="report-title">تقرير حالة الحاويات</h3>
                    </div>
                    <p class="text-muted">عرض شامل لجميع الحاويات مع حالتها الحالية وتفاصيل الربحية</p>

                    <div class="sql-code">
SELECT
    c.container_number AS 'رقم الحاوية',
    c.entry_date AS 'تاريخ الدخول',
    c.exit_date AS 'تاريخ الخروج',
    CASE c.status
        WHEN 'pending' THEN 'في الانتظار'
        WHEN 'in_progress' THEN 'قيد التنفيذ'
        WHEN 'completed' THEN 'مكتملة'
        WHEN 'cancelled' THEN 'ملغية'
    END AS 'الحالة',
    c.continer_content AS 'محتوى الحاوية',
    c.contyner_size AS 'حجم الحاوية',
    c.purchase_price AS 'سعر الشراء',
    c.selling_price AS 'سعر البيع',
    (c.selling_price - c.purchase_price) as 'الربح/الخسارة',
    DATEDIFF(c.exit_date, c.entry_date) as 'أيام التخزين'
FROM containers c
WHERE c.trader_id = ? AND c.deleted_at IS NULL
ORDER BY c.entry_date DESC;
                    </div>
                </div>

                <!-- Container Profitability Report -->
                <div class="report-card">
                    <div class="report-header">
                        <i class="fas fa-chart-line report-icon"></i>
                        <h3 class="report-title">تقرير ربحية الحاويات</h3>
                    </div>
                    <p class="text-muted">تحليل مفصل للربحية لكل حاوية مع احتساب الغرامات والرسوم</p>

                    <div class="sql-code">
SELECT
    c.container_number AS 'رقم الحاوية',
    c.purchase_price AS 'سعر الشراء',
    c.selling_price AS 'سعر البيع',
    (c.selling_price - c.purchase_price) as 'الربح الإجمالي',
    COALESCE(SUM(f.amount), 0) as 'إجمالي الغرامات',
    COALESCE(SUM(of.amount), 0) as 'إجمالي الرسوم',
    ((c.selling_price - c.purchase_price) - COALESCE(SUM(f.amount), 0) - COALESCE(SUM(of.amount), 0)) as 'صافي الربح',
    ROUND(((c.selling_price - c.purchase_price) / c.purchase_price * 100), 2) as 'نسبة الربح %'
FROM containers c
LEFT JOIN fines f ON c.id = f.container_id AND f.deleted_at IS NULL
LEFT JOIN other_fees of ON c.id = of.container_id AND of.deleted_at IS NULL
WHERE c.trader_id = ? AND c.deleted_at IS NULL
GROUP BY c.id
ORDER BY 'صافي الربح' DESC;
                    </div>

                    <div class="chart-container">
                        <canvas id="profitabilityChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <!-- Overdue Containers Report -->
                <div class="report-card">
                    <div class="report-header">
                        <i class="fas fa-exclamation-triangle report-icon text-warning"></i>
                        <h3 class="report-title">تقرير الحاويات المتأخرة</h3>
                    </div>
                    <p class="text-muted">عرض الحاويات التي تجاوزت تاريخ الخروج المحدد</p>

                    <div class="alert alert-warning alert-custom">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تنبيه:</strong> هذا التقرير يعرض الحاويات التي تحتاج إلى متابعة عاجلة
                    </div>

                    <div class="sql-code">
SELECT
    c.container_number AS 'رقم الحاوية',
    c.entry_date AS 'تاريخ الدخول',
    c.exit_date AS 'تاريخ الخروج المحدد',
    DATEDIFF(CURDATE(), c.exit_date) as 'أيام التأخير',
    CASE c.status
        WHEN 'pending' THEN 'في الانتظار'
        WHEN 'in_progress' THEN 'قيد التنفيذ'
        WHEN 'completed' THEN 'مكتملة'
        WHEN 'cancelled' THEN 'ملغية'
    END AS 'الحالة',
    c.continer_content AS 'المحتوى',
    COALESCE(SUM(f.amount), 0) as 'إجمالي الغرامات المطبقة'
FROM containers c
LEFT JOIN fines f ON c.id = f.container_id AND f.deleted_at IS NULL
WHERE c.trader_id = ?
  AND c.exit_date < CURDATE()
  AND c.status != 'completed'
  AND c.deleted_at IS NULL
GROUP BY c.id
ORDER BY 'أيام التأخير' DESC;
                    </div>
                </div>
            </div>

            <!-- Fees and Fines Reports Tab -->
            <div class="tab-pane fade" id="fees-reports">
                <!-- Detailed Fines Report -->
                <div class="report-card">
                    <div class="report-header">
                        <i class="fas fa-gavel report-icon text-danger"></i>
                        <h3 class="report-title">تقرير الغرامات التفصيلي</h3>
                    </div>
                    <p class="text-muted">عرض جميع الغرامات المطبقة مع تفاصيلها وأسبابها</p>

                    <div class="sql-code">
SELECT
    f.fine_date AS 'تاريخ الغرامة',
    c.container_number AS 'رقم الحاوية',
    CASE f.fine_type
        WHEN 'delay' THEN 'غرامة تأخير'
        WHEN 'damages' THEN 'غرامة أضرار'
        ELSE 'غرامة أخرى'
    END as 'نوع الغرامة',
    f.amount AS 'المبلغ',
    f.notes AS 'الملاحظات'
FROM fines f
JOIN containers c ON f.container_id = c.id
WHERE f.trader_id = ? AND f.deleted_at IS NULL
ORDER BY f.fine_date DESC;
                    </div>
                </div>

                <!-- Additional Fees Report -->
                <div class="report-card">
                    <div class="report-header">
                        <i class="fas fa-money-check-alt report-icon text-info"></i>
                        <h3 class="report-title">تقرير الرسوم الإضافية</h3>
                    </div>
                    <p class="text-muted">عرض جميع الرسوم الإضافية المطبقة على الحاويات</p>

                    <div class="sql-code">
SELECT
    of.fee_date AS 'تاريخ الرسوم',
    c.container_number AS 'رقم الحاوية',
    CASE of.types_fee
        WHEN 'returns and fees' THEN 'رسوم إرجاع'
        WHEN 'location fee' THEN 'رسوم موقع'
        WHEN 'handling fee' THEN 'رسوم مناولة'
        ELSE 'رسوم أخرى'
    END as 'نوع الرسوم',
    of.amount AS 'المبلغ',
    of.notes AS 'الملاحظات'
FROM other_fees of
JOIN containers c ON of.container_id = c.id
WHERE of.trader_id = ? AND of.deleted_at IS NULL
ORDER BY of.fee_date DESC;
                    </div>

                    <div class="chart-container">
                        <canvas id="feesChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Advanced Analytics Tab -->
            <div class="tab-pane fade" id="analytics-reports">
                <!-- Annual Performance Report -->
                <div class="report-card">
                    <div class="report-header">
                        <i class="fas fa-trophy report-icon text-success"></i>
                        <h3 class="report-title">تقرير الأداء السنوي</h3>
                    </div>
                    <p class="text-muted">تحليل شامل للأداء السنوي مع مقارنة السنوات</p>

                    <div class="sql-code">
SELECT
    YEAR(document_date) as 'السنة',
    COUNT(CASE WHEN document_type = 'receipt' THEN 1 END) as 'عدد الإيصالات',
    SUM(CASE WHEN document_type = 'receipt' THEN amount ELSE 0 END) as 'إجمالي الإيصالات',
    COUNT(CASE WHEN document_type = 'payment' THEN 1 END) as 'عدد المدفوعات',
    SUM(CASE WHEN document_type = 'payment' THEN amount ELSE 0 END) as 'إجمالي المدفوعات',
    SUM(CASE WHEN document_type = 'expense' THEN amount ELSE 0 END) as 'إجمالي المصروفات',
    (SELECT COUNT(*) FROM containers WHERE trader_id = ? AND YEAR(entry_date) = YEAR(fd.document_date)) as 'عدد الحاويات'
FROM financial_documents fd
WHERE fd.trader_id = ? AND fd.deleted_at IS NULL
GROUP BY YEAR(document_date)
ORDER BY 'السنة' DESC;
                    </div>

                    <div class="chart-container">
                        <canvas id="annualChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <!-- Cash Flow Report -->
                <div class="report-card">
                    <div class="report-header">
                        <i class="fas fa-exchange-alt report-icon text-primary"></i>
                        <h3 class="report-title">تقرير التدفق النقدي</h3>
                    </div>
                    <p class="text-muted">تحليل التدفق النقدي الشهري للتاجر</p>

                    <div class="sql-code">
SELECT
    DATE_FORMAT(document_date, '%Y-%m') as 'الشهر',
    SUM(CASE WHEN document_type IN ('receipt', 'capital') THEN amount ELSE 0 END) as 'التدفق الداخل',
    SUM(CASE WHEN document_type IN ('payment', 'expense') THEN amount ELSE 0 END) as 'التدفق الخارج',
    (SUM(CASE WHEN document_type IN ('receipt', 'capital') THEN amount ELSE 0 END) -
     SUM(CASE WHEN document_type IN ('payment', 'expense') THEN amount ELSE 0 END)) as 'صافي التدفق النقدي'
FROM financial_documents
WHERE trader_id = ? AND deleted_at IS NULL
GROUP BY DATE_FORMAT(document_date, '%Y-%m')
ORDER BY 'الشهر' DESC;
                    </div>

                    <div class="chart-container">
                        <canvas id="cashFlowChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <!-- Comprehensive Summary -->
                <div class="report-card">
                    <div class="report-header">
                        <i class="fas fa-clipboard-list report-icon text-dark"></i>
                        <h3 class="report-title">الملخص الشامل للتاجر</h3>
                    </div>
                    <p class="text-muted">ملخص شامل لجميع أنشطة التاجر ومؤشرات الأداء الرئيسية</p>

                    <div class="sql-code">
SELECT
    t.name as 'اسم التاجر',
    t.code as 'كود التاجر',
    (SELECT COUNT(*) FROM containers WHERE trader_id = t.id AND deleted_at IS NULL) as 'إجمالي الحاويات',
    (SELECT COUNT(*) FROM containers WHERE trader_id = t.id AND status = 'completed' AND deleted_at IS NULL) as 'الحاويات المكتملة',
    (SELECT SUM(amount) FROM financial_documents WHERE trader_id = t.id AND document_type = 'receipt' AND deleted_at IS NULL) as 'إجمالي الإيصالات',
    (SELECT SUM(amount) FROM financial_documents WHERE trader_id = t.id AND document_type = 'payment' AND deleted_at IS NULL) as 'إجمالي المدفوعات',
    (SELECT SUM(amount) FROM fines WHERE trader_id = t.id AND deleted_at IS NULL) as 'إجمالي الغرامات',
    (SELECT SUM(amount) FROM other_fees WHERE trader_id = t.id AND deleted_at IS NULL) as 'إجمالي الرسوم',
    ((SELECT COALESCE(SUM(amount), 0) FROM financial_documents WHERE trader_id = t.id AND document_type IN ('receipt', 'capital') AND deleted_at IS NULL) -
     (SELECT COALESCE(SUM(amount), 0) FROM financial_documents WHERE trader_id = t.id AND document_type IN ('payment', 'expense') AND deleted_at IS NULL)) as 'الرصيد الحالي'
FROM traders t
WHERE t.id = ? AND t.deleted_at IS NULL;
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-primary text-white rounded">
                                <h4 id="totalContainers">--</h4>
                                <small>إجمالي الحاويات</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-success text-white rounded">
                                <h4 id="totalReceipts">--</h4>
                                <small>إجمالي الإيصالات</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-warning text-white rounded">
                                <h4 id="totalFines">--</h4>
                                <small>إجمالي الغرامات</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-info text-white rounded">
                                <h4 id="currentBalance">--</h4>
                                <small>الرصيد الحالي</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Development Suggestions Tab -->
            <div class="tab-pane fade" id="development-suggestions">
                <div class="row">
                    <!-- Visual Reports Suggestions -->
                    <div class="col-md-6">
                        <div class="suggestion-card">
                            <h4><i class="fas fa-chart-pie"></i> التقارير البصرية</h4>
                            <ul class="feature-list">
                                <li>رسوم بيانية للتدفق النقدي الشهري</li>
                                <li>مخططات دائرية لتوزيع أنواع المعاملات</li>
                                <li>رسوم بيانية لربحية الحاويات</li>
                                <li>مخططات مقارنة الأداء السنوي</li>
                                <li>رسوم بيانية للغرامات والرسوم</li>
                            </ul>
                            <button class="btn btn-light mt-3" onclick="implementVisualReports()">
                                <i class="fas fa-code"></i> تطبيق التقارير البصرية
                            </button>
                        </div>
                    </div>

                    <!-- Comparative Reports -->
                    <div class="col-md-6">
                        <div class="suggestion-card">
                            <h4><i class="fas fa-balance-scale"></i> التقارير المقارنة</h4>
                            <ul class="feature-list">
                                <li>مقارنة أداء التاجر مع المتوسط العام</li>
                                <li>مقارنة الأداء بين فترات زمنية مختلفة</li>
                                <li>مقارنة ربحية الحاويات حسب الحجم</li>
                                <li>مقارنة التكاليف والإيرادات</li>
                                <li>تحليل الاتجاهات والتوقعات</li>
                            </ul>
                            <button class="btn btn-light mt-3" onclick="implementComparativeReports()">
                                <i class="fas fa-chart-line"></i> تطبيق التقارير المقارنة
                            </button>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <!-- Smart Alerts -->
                    <div class="col-md-6">
                        <div class="suggestion-card">
                            <h4><i class="fas fa-bell"></i> التنبيهات الذكية</h4>
                            <ul class="feature-list">
                                <li>تنبيهات للحاويات المتأخرة</li>
                                <li>تنبيهات للمدفوعات المستحقة</li>
                                <li>تنبيهات لانخفاض الرصيد</li>
                                <li>تنبيهات للحاويات عالية التكلفة</li>
                                <li>تنبيهات للأنماط غير العادية</li>
                            </ul>
                            <button class="btn btn-light mt-3" onclick="implementSmartAlerts()">
                                <i class="fas fa-exclamation-triangle"></i> تطبيق التنبيهات الذكية
                            </button>
                        </div>
                    </div>

                    <!-- Advanced Features -->
                    <div class="col-md-6">
                        <div class="suggestion-card">
                            <h4><i class="fas fa-cogs"></i> الميزات المتقدمة</h4>
                            <ul class="feature-list">
                                <li>تصدير التقارير إلى PDF/Excel</li>
                                <li>جدولة التقارير التلقائية</li>
                                <li>لوحة معلومات تفاعلية</li>
                                <li>تحليل البيانات بالذكاء الاصطناعي</li>
                                <li>تكامل مع أنظمة المحاسبة</li>
                            </ul>
                            <button class="btn btn-light mt-3" onclick="implementAdvancedFeatures()">
                                <i class="fas fa-rocket"></i> تطبيق الميزات المتقدمة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Implementation Roadmap -->
                <div class="report-card mt-4">
                    <div class="report-header">
                        <i class="fas fa-road report-icon"></i>
                        <h3 class="report-title">خارطة طريق التطوير</h3>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <i class="fas fa-flag text-success fa-2x mb-2"></i>
                                <h5>المرحلة الأولى</h5>
                                <small>التقارير الأساسية</small>
                                <div class="progress mt-2">
                                    <div class="progress-bar bg-success" style="width: 100%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <i class="fas fa-chart-bar text-warning fa-2x mb-2"></i>
                                <h5>المرحلة الثانية</h5>
                                <small>التقارير البصرية</small>
                                <div class="progress mt-2">
                                    <div class="progress-bar bg-warning" style="width: 60%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <i class="fas fa-bell text-info fa-2x mb-2"></i>
                                <h5>المرحلة الثالثة</h5>
                                <small>التنبيهات الذكية</small>
                                <div class="progress mt-2">
                                    <div class="progress-bar bg-info" style="width: 30%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <i class="fas fa-rocket text-primary fa-2x mb-2"></i>
                                <h5>المرحلة الرابعة</h5>
                                <small>الذكاء الاصطناعي</small>
                                <div class="progress mt-2">
                                    <div class="progress-bar bg-primary" style="width: 10%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Global variables
        let currentTrader = null;
        let charts = {};

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            updateLastUpdateTime();

            // Add event listeners
            document.getElementById('traderSelect').addEventListener('change', function() {
                currentTrader = this.value;
                if (currentTrader) {
                    loadTraderData();
                }
            });
        });

        // Update last update time
        function updateLastUpdateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA');
            document.getElementById('lastUpdate').textContent = timeString;
        }

        // Generate reports for selected trader
        function generateReports() {
            const traderId = document.getElementById('traderSelect').value;
            if (!traderId) {
                alert('يرجى اختيار التاجر أولاً');
                return;
            }

            currentTrader = traderId;
            loadTraderData();
            updateLastUpdateTime();

            // Show success message
            showNotification('تم إنشاء التقارير بنجاح', 'success');
        }

        // Load trader data and update charts
        function loadTraderData() {
            if (!currentTrader) return;

            // Simulate loading data (in real implementation, this would be AJAX calls)
            setTimeout(() => {
                updateSummaryCards();
                updateCharts();
                showNotification('تم تحديث البيانات', 'info');
            }, 1000);
        }

        // Update summary cards with sample data
        function updateSummaryCards() {
            // Sample data - in real implementation, this would come from database
            const sampleData = {
                totalContainers: Math.floor(Math.random() * 50) + 10,
                totalReceipts: (Math.random() * 1000000 + 100000).toFixed(0),
                totalFines: (Math.random() * 50000 + 5000).toFixed(0),
                currentBalance: (Math.random() * 500000 + 50000).toFixed(0)
            };

            document.getElementById('totalContainers').textContent = sampleData.totalContainers;
            document.getElementById('totalReceipts').textContent = formatCurrency(sampleData.totalReceipts);
            document.getElementById('totalFines').textContent = formatCurrency(sampleData.totalFines);
            document.getElementById('currentBalance').textContent = formatCurrency(sampleData.currentBalance);
        }

        // Initialize all charts
        function initializeCharts() {
            initializeMonthlyChart();
            initializeProfitabilityChart();
            initializeFeesChart();
            initializeAnnualChart();
            initializeCashFlowChart();
        }

        // Initialize monthly income/expense chart
        function initializeMonthlyChart() {
            const ctx = document.getElementById('monthlyChart').getContext('2d');
            charts.monthly = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'الإيرادات',
                        data: [120000, 150000, 180000, 140000, 200000, 160000],
                        backgroundColor: 'rgba(52, 152, 219, 0.8)',
                        borderColor: 'rgba(52, 152, 219, 1)',
                        borderWidth: 2
                    }, {
                        label: 'المصروفات',
                        data: [80000, 90000, 110000, 85000, 120000, 95000],
                        backgroundColor: 'rgba(231, 76, 60, 0.8)',
                        borderColor: 'rgba(231, 76, 60, 1)',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'الإيرادات والمصروفات الشهرية'
                        },
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatCurrency(value);
                                }
                            }
                        }
                    }
                }
            });
        }

        // Initialize profitability chart
        function initializeProfitabilityChart() {
            const ctx = document.getElementById('profitabilityChart').getContext('2d');
            charts.profitability = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['حاوية 1', 'حاوية 2', 'حاوية 3', 'حاوية 4', 'حاوية 5'],
                    datasets: [{
                        label: 'صافي الربح',
                        data: [25000, 35000, 15000, 45000, 30000],
                        backgroundColor: 'rgba(46, 204, 113, 0.2)',
                        borderColor: 'rgba(46, 204, 113, 1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'ربحية الحاويات'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatCurrency(value);
                                }
                            }
                        }
                    }
                }
            });
        }

        // Initialize fees chart
        function initializeFeesChart() {
            const ctx = document.getElementById('feesChart').getContext('2d');
            charts.fees = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['رسوم إرجاع', 'رسوم موقع', 'رسوم مناولة', 'رسوم أخرى'],
                    datasets: [{
                        data: [30, 25, 35, 10],
                        backgroundColor: [
                            'rgba(52, 152, 219, 0.8)',
                            'rgba(46, 204, 113, 0.8)',
                            'rgba(241, 196, 15, 0.8)',
                            'rgba(155, 89, 182, 0.8)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'توزيع أنواع الرسوم'
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Initialize annual chart
        function initializeAnnualChart() {
            const ctx = document.getElementById('annualChart').getContext('2d');
            charts.annual = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['2022', '2023', '2024', '2025'],
                    datasets: [{
                        label: 'إجمالي الإيرادات',
                        data: [800000, 950000, 1200000, 600000],
                        backgroundColor: 'rgba(52, 152, 219, 0.8)',
                        borderColor: 'rgba(52, 152, 219, 1)',
                        borderWidth: 2
                    }, {
                        label: 'عدد الحاويات',
                        data: [25, 32, 45, 20],
                        backgroundColor: 'rgba(46, 204, 113, 0.8)',
                        borderColor: 'rgba(46, 204, 113, 1)',
                        borderWidth: 2,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'الأداء السنوي'
                        }
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            ticks: {
                                callback: function(value) {
                                    return formatCurrency(value);
                                }
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }

        // Initialize cash flow chart
        function initializeCashFlowChart() {
            const ctx = document.getElementById('cashFlowChart').getContext('2d');
            charts.cashFlow = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'التدفق الداخل',
                        data: [120000, 150000, 180000, 140000, 200000, 160000],
                        backgroundColor: 'rgba(46, 204, 113, 0.2)',
                        borderColor: 'rgba(46, 204, 113, 1)',
                        borderWidth: 3,
                        fill: false
                    }, {
                        label: 'التدفق الخارج',
                        data: [80000, 90000, 110000, 85000, 120000, 95000],
                        backgroundColor: 'rgba(231, 76, 60, 0.2)',
                        borderColor: 'rgba(231, 76, 60, 1)',
                        borderWidth: 3,
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'التدفق النقدي الشهري'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatCurrency(value);
                                }
                            }
                        }
                    }
                }
            });
        }

        // Update all charts with new data
        function updateCharts() {
            // Generate random data for demonstration
            Object.keys(charts).forEach(chartKey => {
                if (charts[chartKey] && charts[chartKey].data) {
                    charts[chartKey].data.datasets.forEach(dataset => {
                        dataset.data = dataset.data.map(() => Math.floor(Math.random() * 200000) + 10000);
                    });
                    charts[chartKey].update();
                }
            });
        }

        // Execute SQL query (simulation)
        function executeQuery(queryType) {
            if (!currentTrader) {
                alert('يرجى اختيار التاجر أولاً');
                return;
            }

            showNotification('جاري تنفيذ الاستعلام...', 'info');

            // Simulate query execution
            setTimeout(() => {
                showNotification('تم تنفيذ الاستعلام بنجاح', 'success');
                // In real implementation, this would show actual results
                displayQueryResults(queryType);
            }, 2000);
        }

        // Display query results
        function displayQueryResults(queryType) {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">نتائج الاستعلام - ${getQueryTitle(queryType)}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>النوع</th>
                                            <th>المبلغ</th>
                                            <th>الملاحظات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${generateSampleRows()}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="exportToExcel()">تصدير Excel</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();

            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        }

        // Get query title
        function getQueryTitle(queryType) {
            const titles = {
                'statement': 'كشف حساب التاجر',
                'monthly': 'التقرير الشهري',
                'containers': 'تقرير الحاويات',
                'fines': 'تقرير الغرامات'
            };
            return titles[queryType] || 'تقرير عام';
        }

        // Generate sample table rows
        function generateSampleRows() {
            let rows = '';
            for (let i = 0; i < 5; i++) {
                rows += `
                    <tr>
                        <td>2025-08-${String(i + 1).padStart(2, '0')}</td>
                        <td>إيصال استلام</td>
                        <td>${formatCurrency(Math.floor(Math.random() * 100000) + 10000)}</td>
                        <td>عملية تجارية رقم ${i + 1}</td>
                    </tr>
                `;
            }
            return rows;
        }

        // Export to PDF
        function exportToPDF() {
            if (!currentTrader) {
                alert('يرجى اختيار التاجر أولاً');
                return;
            }

            showNotification('جاري تصدير التقرير إلى PDF...', 'info');

            // Simulate PDF export
            setTimeout(() => {
                showNotification('تم تصدير التقرير بنجاح', 'success');
                // In real implementation, this would generate and download PDF
            }, 3000);
        }

        // Export to Excel
        function exportToExcel() {
            showNotification('جاري تصدير البيانات إلى Excel...', 'info');

            // Simulate Excel export
            setTimeout(() => {
                showNotification('تم تصدير البيانات بنجاح', 'success');
                // In real implementation, this would generate and download Excel file
            }, 2000);
        }

        // Implementation functions for development suggestions
        function implementVisualReports() {
            showNotification('جاري تطبيق التقارير البصرية...', 'info');
            setTimeout(() => {
                showNotification('تم تطبيق التقارير البصرية بنجاح', 'success');
                updateCharts();
            }, 2000);
        }

        function implementComparativeReports() {
            showNotification('جاري تطبيق التقارير المقارنة...', 'info');
            setTimeout(() => {
                showNotification('تم تطبيق التقارير المقارنة بنجاح', 'success');
            }, 2000);
        }

        function implementSmartAlerts() {
            showNotification('جاري تطبيق التنبيهات الذكية...', 'info');
            setTimeout(() => {
                showNotification('تم تطبيق التنبيهات الذكية بنجاح', 'success');
                showSmartAlert();
            }, 2000);
        }

        function implementAdvancedFeatures() {
            showNotification('جاري تطبيق الميزات المتقدمة...', 'info');
            setTimeout(() => {
                showNotification('تم تطبيق الميزات المتقدمة بنجاح', 'success');
            }, 2000);
        }

        // Show smart alert example
        function showSmartAlert() {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-warning alert-dismissible fade show position-fixed';
            alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; max-width: 400px;';
            alertDiv.innerHTML = `
                <i class="fas fa-exclamation-triangle"></i>
                <strong>تنبيه ذكي:</strong> يوجد 3 حاويات متأخرة تحتاج إلى متابعة عاجلة
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }

        // Format currency
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR',
                minimumFractionDigits: 0
            }).format(amount);
        }

        // Print report
        function printReport() {
            window.print();
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey) {
                switch(e.key) {
                    case 'p':
                        e.preventDefault();
                        printReport();
                        break;
                    case 's':
                        e.preventDefault();
                        exportToPDF();
                        break;
                    case 'r':
                        e.preventDefault();
                        generateReports();
                        break;
                }
            }
        });

        // Auto-refresh data every 5 minutes
        setInterval(() => {
            if (currentTrader) {
                loadTraderData();
            }
        }, 300000);

        // Add tooltips to all elements with title attribute
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
</body>
</html>
