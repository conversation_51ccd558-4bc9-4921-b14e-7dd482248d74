-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Aug 18, 2025 at 11:35 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `ccis_system`
--

-- --------------------------------------------------------

--
-- Table structure for table `traders`
--

CREATE TABLE `traders` (
  `id` int(11) NOT NULL,
  `code` varchar(11) NOT NULL COMMENT 'رقم الحساب',
  `name` varchar(255) NOT NULL,
  `phone` varchar(50) NOT NULL,
  `email` varchar(50) NOT NULL,
  `address` varchar(255) NOT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `traders`
--

INSERT INTO `traders` (`id`, `code`, `name`, `phone`, `email`, `address`, `notes`, `created_at`, `created_by`, `deleted_at`, `deleted_by`, `updated_at`, `updated_by`) VALUES
(1, 'count', 'عبدالرضا طاهر حسين', '07803883850', '<EMAIL>', 'العراق , البصرة , شارع الجزائر565', NULL, '2025-07-29 21:00:00', -1, NULL, NULL, NULL, NULL),
(2, 'acc123', 'علي', '354354', '<EMAIL>', 'shat alarab', NULL, '2025-07-30 21:00:00', -1, NULL, NULL, '2025-08-06 05:27:56', -1),
(3, 'acc124', 'reda', '123123213', '<EMAIL>', 'dfsfsdfsd', 'sdfsd', '2025-08-05 21:00:00', -1, NULL, NULL, NULL, NULL),
(4, 'ccx11231', 'scsd', '3453453', '<EMAIL>', 'dfgfdg', NULL, '2025-08-09 21:00:00', -1, NULL, NULL, NULL, NULL);

--
-- Triggers `traders`
--
DELIMITER $$
CREATE TRIGGER `after_trader_insert` AFTER INSERT ON `traders` FOR EACH ROW BEGIN
    INSERT INTO financial_funds (name, code)
    VALUES (NEW.name, NEW.code);
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `after_trader_update` AFTER UPDATE ON `traders` FOR EACH ROW BEGIN
    -- تحديث البيانات في جدول financial_funds بناءً على الـ code القديم
    UPDATE financial_funds 
    SET name = NEW.name, code = NEW.code
    WHERE code = OLD.code;
END
$$
DELIMITER ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `traders`
--
ALTER TABLE `traders`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `traders`
--
ALTER TABLE `traders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
