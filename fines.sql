-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Aug 18, 2025 at 11:35 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `ccis_system`
--

-- --------------------------------------------------------

--
-- Table structure for table `fines`
--

CREATE TABLE `fines` (
  `id` int(11) NOT NULL,
  `trader_id` int(11) NOT NULL,
  `container_id` int(11) NOT NULL,
  `fine_type` enum('delay','damages','other','') NOT NULL,
  `amount` int(11) NOT NULL,
  `fine_date` date NOT NULL,
  `notes` text NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `deleted_by` int(11) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `fines`
--

INSERT INTO `fines` (`id`, `trader_id`, `container_id`, `fine_type`, `amount`, `fine_date`, `notes`, `created_by`, `created_at`, `deleted_by`, `deleted_at`, `updated_by`, `updated_at`) VALUES
(1, 2, 2, 'delay', 5000, '2025-08-05', 'mbv', -1, '2025-08-10 20:06:18', NULL, NULL, NULL, NULL),
(2, 2, 2, 'damages', 5000000, '2025-08-06', 'fhfghf', -1, '2025-08-17 19:56:00', NULL, NULL, NULL, NULL),
(3, 3, 3, 'damages', 500000, '2025-08-20', 'يبلبي', -1, '2025-08-18 07:27:49', NULL, NULL, NULL, NULL);

--
-- Triggers `fines`
--
DELIMITER $$
CREATE TRIGGER `after_fines_insert` AFTER INSERT ON `fines` FOR EACH ROW BEGIN
    DECLARE voucher_id INT;
    DECLARE trader_fund_id INT;
    DECLARE fines_account_id INT DEFAULT 1;
    DECLARE doc_type_code VARCHAR(10);
    DECLARE doc_number VARCHAR(20);
    
    -- تحديد كود نوع الغرامة
    SET doc_type_code = CASE NEW.fine_type
        WHEN 'delay' THEN 'FINE_DELAY'
        WHEN 'damages' THEN 'FINE_DAMAGE'
        ELSE 'FINE_OTHER'
    END;
    
    -- توليد رقم الغرامة
    SET doc_number = get_next_document_number(doc_type_code);
    
    -- الحصول على معرف حساب التاجر
    SELECT f.id INTO trader_fund_id 
    FROM financial_funds f
    INNER JOIN traders t ON f.code = t.code
    WHERE t.id = NEW.trader_id;
    
    -- إنشاء قسيمة جديدة
    INSERT INTO voucher (no, document_number, document_type_code, type_id, date)
    VALUES (NEW.id, doc_number, doc_type_code, 2, NEW.fine_date);
    
    SET voucher_id = LAST_INSERT_ID();
    
    -- القيد المحاسبي للغرامة
    INSERT INTO voucher_account (voucher_id, date, depit, credit, note, account_id)
    VALUES 
        (voucher_id, NEW.fine_date, NEW.amount, 0, 
         CONCAT('غرامة ', NEW.fine_type, ' ', doc_number, ' - حاوية ', NEW.container_id), trader_fund_id),
        (voucher_id, NEW.fine_date, 0, NEW.amount, 
         CONCAT('إيراد غرامة ', doc_number), fines_account_id);
END
$$
DELIMITER ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `fines`
--
ALTER TABLE `fines`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `fines`
--
ALTER TABLE `fines`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
