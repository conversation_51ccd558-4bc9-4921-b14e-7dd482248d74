-- =====================================================
-- phpMyAdmin SQL Dump
-- =====================================================
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Aug 18, 2025 at 11:35 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12
-- =====================================================

-- =====================================================
-- Database Configuration
-- =====================================================
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- Character Set Configuration
/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- =====================================================
-- Database: `ccis_system`
-- =====================================================

-- =====================================================
-- Table structure for table `other_fees`
-- =====================================================

CREATE TABLE `other_fees` (
  `id`           int(11)    NOT NULL,
  `trader_id`    int(11)    NOT NULL,
  `container_id` int(11)    NOT NULL,
  `types_fee`    enum('returns and fees','location fee','handling fee','other') NOT NULL,
  `amount`       int(11)    NOT NULL,
  `fee_date`     date       NOT NULL,
  `notes`        text       NOT NULL,
  `created_by`   int(11)    NOT NULL,
  `created_at`   timestamp  NOT NULL DEFAULT current_timestamp(),
  `deleted_by`   int(11)    DEFAULT NULL,
  `deleted_at`   date       DEFAULT NULL,
  `updated_by`   int(11)    DEFAULT NULL,
  `updated_at`   date       DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- Sample data for table `other_fees`
-- =====================================================

INSERT INTO `other_fees` (
  `id`, `trader_id`, `container_id`, `types_fee`, `amount`, `fee_date`,
  `notes`, `created_by`, `created_at`, `deleted_by`, `deleted_at`, `updated_by`, `updated_at`
) VALUES
(1, 3, 3, 'returns and fees', 500000, '2025-08-07', 'nhjbj', -1, '2025-08-18 07:41:12', NULL, NULL, NULL, NULL);

-- =====================================================
-- Triggers for table `other_fees`
-- =====================================================

DELIMITER $$
CREATE TRIGGER `after_other_fees_insert`
AFTER INSERT ON `other_fees`
FOR EACH ROW
BEGIN
    -- Variable declarations
    DECLARE voucher_id      INT;
    DECLARE trader_fund_id  INT;
    DECLARE fees_account_id INT DEFAULT 1;
    DECLARE doc_type_code   VARCHAR(10);
    DECLARE doc_number      VARCHAR(20);

    -- Determine fee type code
    SET doc_type_code = CASE NEW.types_fee
        WHEN 'location fee'     THEN 'FEE_LOCATION'
        WHEN 'handling fee'     THEN 'FEE_HANDLING'
        WHEN 'returns and fees' THEN 'FEE_RETURN'
        ELSE 'FEE_OTHER'
    END;

    -- Generate fee number
    SET doc_number = get_next_document_number(doc_type_code);

    -- Get trader account ID
    SELECT f.id INTO trader_fund_id
    FROM financial_funds f
    INNER JOIN traders t ON f.code = t.code
    WHERE t.id = NEW.trader_id;

    -- Create new voucher
    INSERT INTO voucher (no, document_number, document_type_code, type_id, date)
    VALUES (NEW.id, doc_number, doc_type_code, 3, NEW.fee_date);

    SET voucher_id = LAST_INSERT_ID();

    -- Accounting entry for fees
    INSERT INTO voucher_account (voucher_id, date, depit, credit, note, account_id)
    VALUES
        (voucher_id, NEW.fee_date, NEW.amount, 0,
         CONCAT('رسوم ', NEW.types_fee, ' ', doc_number, ' - حاوية ', NEW.container_id),
         trader_fund_id),
        (voucher_id, NEW.fee_date, 0, NEW.amount,
         CONCAT('إيراد رسوم ', doc_number),
         fees_account_id);
END
$$
DELIMITER ;

-- =====================================================
-- Table Indexes
-- =====================================================

ALTER TABLE `other_fees`
  ADD PRIMARY KEY (`id`);

-- =====================================================
-- Auto Increment Settings
-- =====================================================

ALTER TABLE `other_fees`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

-- =====================================================
-- Commit Transaction
-- =====================================================

COMMIT;

-- =====================================================
-- Restore Character Set Settings
-- =====================================================

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
