-- =====================================================
-- phpMyAdmin SQL Dump
-- =====================================================
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Aug 18, 2025 at 11:35 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12
-- =====================================================

-- =====================================================
-- Database Configuration
-- =====================================================
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- Character Set Configuration
/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- =====================================================
-- Database: `ccis_system`
-- =====================================================

-- =====================================================
-- Table structure for table `containers`
-- =====================================================

CREATE TABLE `containers` (
  `id`               int(11)      NOT NULL,
  `container_number` varchar(50)  NOT NULL,
  `company_id`       int(11)      NOT NULL,
  `trader_id`        int(11)      NOT NULL,
  `entry_date`       date         NOT NULL,
  `exit_date`        date         NOT NULL,
  `status`           enum('pending','in_progress','completed','cancelled') NOT NULL DEFAULT 'pending',
  `continer_content` varchar(200) NOT NULL,
  `contyner_size`    enum('20','40','45','') NOT NULL,
  `purchase_price`   int(50)      NOT NULL,
  `selling_price`    int(50)      NOT NULL,
  `notes`            text         DEFAULT NULL,
  `created_by`       int(11)      NOT NULL,
  `created_at`       timestamp    NOT NULL DEFAULT current_timestamp(),
  `deleted_at`       datetime     DEFAULT NULL,
  `deleted_by`       int(11)      DEFAULT NULL,
  `updated_at`       datetime     DEFAULT NULL,
  `updated_by`       int(11)      DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- Sample data for table `containers`
-- =====================================================

INSERT INTO `containers` (
  `id`, `container_number`, `company_id`, `trader_id`, `entry_date`, `exit_date`,
  `status`, `continer_content`, `contyner_size`, `purchase_price`, `selling_price`,
  `notes`, `created_by`, `created_at`, `deleted_at`, `deleted_by`, `updated_at`, `updated_by`
) VALUES
(1, 'mbngh23423423', 1, 1, '2025-07-30', '2025-07-30', 'pending', 'dfgdfg', '40',
 34234234, *********, 'fgdfgfd', -1, '2025-07-30 21:00:00', NULL, NULL, '2025-07-31 10:35:21', -1);

-- =====================================================
-- Table Indexes
-- =====================================================

ALTER TABLE `containers`
  ADD PRIMARY KEY (`id`);

-- =====================================================
-- Auto Increment Settings
-- =====================================================

ALTER TABLE `containers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

-- =====================================================
-- Commit Transaction
-- =====================================================

COMMIT;

-- =====================================================
-- Restore Character Set Settings
-- =====================================================

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
