-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Aug 18, 2025 at 11:35 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `ccis_system`
--

-- --------------------------------------------------------

--
-- Table structure for table `financial_documents`
--

CREATE TABLE `financial_documents` (
  `id` int(11) NOT NULL,
  `fund_type_id` int(11) NOT NULL,
  `document_type` enum('receipt','payment','expense','transfer','capital') NOT NULL,
  `document_date` date NOT NULL,
  `amount` int(11) NOT NULL,
  `trader_id` int(11) NOT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `financial_documents`
--

INSERT INTO `financial_documents` (`id`, `fund_type_id`, `document_type`, `document_date`, `amount`, `trader_id`, `notes`, `created_by`, `created_at`, `deleted_at`, `deleted_by`, `updated_at`, `updated_by`) VALUES
(1, 0, 'receipt', '2025-07-31', 23543534, 1, 'ertret', -1, '2025-07-01 21:00:00', NULL, NULL, NULL, NULL),
(2, 0, 'payment', '2025-07-31', 5000000, 1, NULL, -1, '2025-07-30 21:00:00', NULL, NULL, NULL, NULL),
(3, 0, 'receipt', '2025-07-09', 500000, 1, 'نةننتىنت', -1, '2025-07-30 21:00:00', NULL, NULL, NULL, NULL),
(4, 1, 'receipt', '2025-07-16', 35345, 2, NULL, -1, '2025-07-30 21:00:00', NULL, NULL, NULL, NULL),
(5, 1, 'receipt', '2025-08-01', 400000, 1, 'بيلي', 0, '2025-08-01 11:48:01', NULL, NULL, NULL, NULL),
(6, 1, 'receipt', '2025-08-06', 300000, 2, 'ؤرلاؤر', 0, '2025-08-01 11:48:50', NULL, NULL, NULL, NULL),
(7, 2, 'receipt', '2025-08-11', 300000, 2, 'بيليب', 0, '2025-07-31 21:00:00', NULL, NULL, NULL, NULL),
(8, 2, 'receipt', '2025-08-18', 200000, 1, 'بللارلاىرىرلا', 0, '2025-08-01 11:58:01', NULL, NULL, NULL, NULL),
(9, 2, 'receipt', '2025-08-06', 8000, 1, 'ffbdfg', -1, '2025-08-06 21:00:00', NULL, NULL, NULL, NULL),
(11, 2, 'receipt', '2025-08-06', 50000, 3, 'تانتنلت', -1, '2025-08-06 21:00:00', NULL, NULL, NULL, NULL),
(12, 1, 'payment', '2025-08-06', 1500000, 2, NULL, -1, '2025-08-06 21:00:00', NULL, NULL, NULL, NULL),
(13, 2, 'expense', '2025-08-07', 100000, 1, NULL, -1, '2025-08-06 21:00:00', NULL, NULL, NULL, NULL),
(14, 2, 'expense', '2025-08-07', 200000, 1, NULL, -1, '2025-08-06 21:00:00', NULL, NULL, NULL, NULL),
(15, 2, 'payment', '2025-08-08', 300000, 1, NULL, -1, '2025-08-06 21:00:00', NULL, NULL, NULL, NULL),
(16, 4, 'payment', '2025-08-09', 400000, 1, NULL, -1, '2025-08-08 21:00:00', NULL, NULL, NULL, NULL),
(17, 2, 'receipt', '2025-08-10', 300000, 3, 'يصيسبسي', -1, '2025-08-09 21:00:00', NULL, NULL, NULL, NULL),
(18, 3, 'payment', '2025-08-10', 500000, 1, NULL, -1, '2025-08-09 21:00:00', NULL, NULL, NULL, NULL),
(19, 3, 'receipt', '2025-08-15', 500000, 1, NULL, -1, '2025-08-16 11:05:49', NULL, NULL, NULL, NULL),
(20, 3, 'payment', '2025-08-15', 400000, 1, NULL, -1, '2025-08-16 11:06:19', NULL, NULL, NULL, NULL),
(21, 3, 'expense', '2025-08-15', 700000, 1, NULL, -1, '2025-08-16 11:06:41', NULL, NULL, NULL, NULL),
(22, 2, 'receipt', '2025-08-18', 500000, 3, NULL, -1, '2025-08-18 07:16:07', NULL, NULL, NULL, NULL),
(23, 3, 'payment', '2025-08-18', 30000, 3, 'رىلال', -1, '2025-08-18 07:17:58', NULL, NULL, NULL, NULL);

--
-- Triggers `financial_documents`
--
DELIMITER $$
CREATE TRIGGER `after_financial_doc_insert` AFTER INSERT ON `financial_documents` FOR EACH ROW BEGIN
    DECLARE voucher_id INT;
    DECLARE fund_account_id INT;
    DECLARE trader_fund_id INT;
    DECLARE doc_type_code VARCHAR(10);
    DECLARE doc_number VARCHAR(20);
    
    -- تحديد كود نوع المستند
    SET doc_type_code = CASE NEW.document_type
        WHEN 'receipt' THEN 'RECEIPT'
        WHEN 'payment' THEN 'PAYMENT'
        WHEN 'expense' THEN 'EXPENSE'
        WHEN 'transfer' THEN 'TRANSFER'
        WHEN 'capital' THEN 'CAPITAL'
        ELSE 'RECEIPT'
    END;
    
    -- توليد رقم المستند
    SET doc_number = get_next_document_number(doc_type_code);
    
    -- الحصول على معرف صندوق التاجر
    SELECT ff.id INTO trader_fund_id 
    FROM financial_funds ff
    WHERE ff.id = NEW.fund_type_id;
    
    -- الحصول على معرف حساب التاجر من خلال الكود
    SELECT f.id INTO fund_account_id 
    FROM financial_funds f
    INNER JOIN traders t ON f.code = t.code
    WHERE t.id = NEW.trader_id;
    
    -- إنشاء قسيمة جديدة مع الرقم المخصص
    INSERT INTO voucher (no, document_number, document_type_code, type_id, date)
    VALUES (NEW.id, doc_number, doc_type_code, 1, NEW.document_date);
    
    SET voucher_id = LAST_INSERT_ID();
    
    -- تطبيق القيود المحاسبية
    IF NEW.document_type = 'receipt' THEN
        INSERT INTO voucher_account (voucher_id, date, depit, credit, note, account_id)
        VALUES 
            (voucher_id, NEW.document_date, NEW.amount, 0.00, CONCAT('إيصال استلام ', doc_number), trader_fund_id),
            (voucher_id, NEW.document_date,  0.00, NEW.amount, CONCAT('من العميل - ', doc_number), NEW.fund_type_id);
            
            
            
    ELSEIF NEW.document_type = 'payment' THEN
        INSERT INTO voucher_account (voucher_id, date, depit, credit, note, account_id)
        VALUES 
            (voucher_id, NEW.document_date,  0.00, NEW.amount, CONCAT('إيصال دفع ', doc_number), trader_fund_id),
            
            (voucher_id, NEW.document_date, NEW.amount, 0.00, CONCAT('للعميل - ', doc_number), NEW.fund_type_id);
            
            
            
    ELSEIF NEW.document_type = 'expense' THEN
        INSERT INTO voucher_account (voucher_id, date, depit, credit, note, account_id)
        VALUES 
            (voucher_id, NEW.document_date, NEW.amount, 0.00, CONCAT('مصروف ', doc_number), trader_fund_id),
            
            (voucher_id, NEW.document_date, 0.00, NEW.amount, CONCAT('مصروف - ', doc_number), NEW.fund_type_id);
            
            
            
    ELSEIF NEW.document_type = 'transfer' THEN
        INSERT INTO voucher_account (voucher_id, date, depit, credit, note, account_id)
        VALUES 
            (voucher_id, NEW.document_date, NEW.amount, 0.00, CONCAT('تحويل وارد ', doc_number), NEW.fund_type_id),
            (voucher_id, NEW.document_date, NEW.document_date, 0.00, CONCAT('تحويل صادر ', doc_number), trader_fund_id);
            
            
    ELSEIF NEW.document_type = 'capital' THEN
        INSERT INTO voucher_account (voucher_id, date, depit, credit, note, account_id)
        VALUES 
            (voucher_id, NEW.document_date,NEW.amount, 0.00, CONCAT('رأس مال ', doc_number), trader_fund_id),
            (voucher_id, NEW.document_date, 0.00, NEW.amount, CONCAT('رأس مال ', doc_number), NEW.fund_type_id);
    END IF;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `after_financial_doc_update` AFTER UPDATE ON `financial_documents` FOR EACH ROW BEGIN
    -- حذف القيود القديمة
    DELETE va FROM voucher_account va
    INNER JOIN voucher v ON va.voucher_id = v.id
    WHERE v.no = OLD.id AND v.type_id = 1;
    
    DELETE FROM voucher WHERE no = OLD.id AND type_id = 1;
    
    -- إعادة إنشاء القيود الجديدة إذا لم يكن محذوف
    IF NEW.deleted_at IS NULL THEN
        CALL create_financial_entry(NEW.id, NEW.fund_type_id, NEW.document_type, 
                                   NEW.document_date, NEW.amount, NEW.trader_id);
    END IF;
END
$$
DELIMITER ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `financial_documents`
--
ALTER TABLE `financial_documents`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `financial_documents`
--
ALTER TABLE `financial_documents`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
